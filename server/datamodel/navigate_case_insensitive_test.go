package datamodel

import (
	"net/http"
	"net/http/httptest"
	"testing"

	"github.com/gin-gonic/gin"
	"github.com/stretchr/testify/assert"
)

func TestGetQueryParamCaseInsensitive(t *testing.T) {
	gin.SetMode(gin.TestMode)

	tests := []struct {
		name           string
		queryParams    string
		expectedParam  string
		expectedValue  string
	}{
		{
			name:           "exact match snake_case",
			queryParams:    "name_exact=test_value",
			expectedParam:  "name_exact",
			expectedValue:  "test_value",
		},
		{
			name:           "uppercase parameter",
			queryParams:    "NAME_EXACT=test_value",
			expectedParam:  "name_exact",
			expectedValue:  "test_value",
		},
		{
			name:           "mixed case parameter",
			queryParams:    "Name_Exact=test_value",
			expectedParam:  "name_exact",
			expectedValue:  "test_value",
		},
		{
			name:           "camelCase parameter",
			queryParams:    "nameExact=test_value",
			expectedParam:  "name_exact",
			expectedValue:  "", // This won't match because we're looking for snake_case
		},
		{
			name:           "description_text variations",
			queryParams:    "DESCRIPTION_TEXT=some_description",
			expectedParam:  "description_text",
			expectedValue:  "some_description",
		},
		{
			name:           "name_text mixed case",
			queryParams:    "Name_Text=search_term",
			expectedParam:  "name_text",
			expectedValue:  "search_term",
		},
		{
			name:           "non-existent parameter",
			queryParams:    "other_param=value",
			expectedParam:  "name_exact",
			expectedValue:  "",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			// Create a test request with query parameters
			req, _ := http.NewRequest("GET", "/test?"+tt.queryParams, nil)
			w := httptest.NewRecorder()
			c, _ := gin.CreateTestContext(w)
			c.Request = req

			// Test the function
			result := getQueryParamCaseInsensitive(c, tt.expectedParam)
			assert.Equal(t, tt.expectedValue, result)
		})
	}
}

func TestGetQueryParamCaseInsensitive_MultipleParams(t *testing.T) {
	gin.SetMode(gin.TestMode)

	// Test with multiple parameters in different cases
	req, _ := http.NewRequest("GET", "/test?NAME_EXACT=exact_value&description_TEXT=desc_value&Name_Text=text_value", nil)
	w := httptest.NewRecorder()
	c, _ := gin.CreateTestContext(w)
	c.Request = req

	// Test each parameter
	assert.Equal(t, "exact_value", getQueryParamCaseInsensitive(c, "name_exact"))
	assert.Equal(t, "desc_value", getQueryParamCaseInsensitive(c, "description_text"))
	assert.Equal(t, "text_value", getQueryParamCaseInsensitive(c, "name_text"))
}
