package datamodel_test

import (
	"context"
	"encoding/json"
	"net/http"

	"github.com/gin-gonic/gin"

	"github.com/BackOfficeAssoc/catalog/fixtures"
	"github.com/BackOfficeAssoc/catalog/models"
	"github.com/BackOfficeAssoc/catalog/repo/dto"
	"github.com/BackOfficeAssoc/catalog/server/datamodel"
	authorizertesting "github.com/BackOfficeAssoc/qzar/pkg/authorizer/authorizer_testing"
)

func (s *ChainTestSuite) TestFindEntities_Success() {
	// Prepare test data
	testUserID := "pcp_123"
	testTenantID := "tnt_1"
	expectedStatus := http.StatusOK
	datamodelContextID := "dst_111"

	// Expected response data
	expectedEntities := dto.Entities{
		{ID: "ent_1", Name: "Entity1", Type: "TABLE", ContextID: datamodelContextID, TenantID: testTenantID, MetaVersion: "v1"},
		{ID: "ent_2", Name: "Entity2", Type: "VIEW", ContextID: datamodelContextID, TenantID: testTenantID, MetaVersion: "v1"},
	}

	// Mock repository
	repo := &fixtures.RepoMock{
		FindEntitiesFunc: func(ctx context.Context, query dto.FindEntitiesQuery) (dto.Entities, error) {
			return expectedEntities, nil
		},
	}

	// Invoke the handler
	authz := authorizertesting.New(testUserID)
	authz.WithTenant(&testTenantID)
	mw := []gin.HandlerFunc{authz.Middleware()}
	handlers := append(mw, datamodel.Find(repo)...)

	w, err := s.NewRequest("GET").
		WithPath("context_id", datamodelContextID).
		Send(handlers...)
	s.Require().NoError(err, "failed to send request")

	// Verify the response
	if !s.Equal(expectedStatus, w.Code) {
		s.FailNow(w.Body.String())
	}

	// Parse response
	var entities []models.Entity
	err = json.Unmarshal(w.Body.Bytes(), &entities)
	s.Require().NoError(err, "failed to unmarshal response")

	// Verify response content
	s.Equal(2, len(entities))
	s.Equal("ent_1", entities[0].ID)
	s.Equal("Entity1", entities[0].Name)
	s.Equal("TABLE", entities[0].Type)
	s.Equal("ent_2", entities[1].ID)
	s.Equal("Entity2", entities[1].Name)
	s.Equal("VIEW", entities[1].Type)

	// Verify the repository calls
	calls := repo.FindEntitiesCalls()
	s.Require().Equal(1, len(calls), "Must make exactly 1 repo.FindEntitiesCalls")
	s.Equal(testTenantID, calls[0].Query.TenantID)
	s.Equal(datamodelContextID, calls[0].Query.ContextID)
}

func (s *ChainTestSuite) TestFindEntities_WithFilters() {
	// Prepare test data
	testUserID := "pcp_123"
	testTenantID := "tnt_1"
	expectedStatus := http.StatusOK
	datamodelContextID := "dst_111"

	// Expected response data
	expectedEntities := dto.Entities{
		{ID: "ent_1", Name: "TableEntity", Type: "TABLE", ContextID: datamodelContextID, TenantID: testTenantID, MetaVersion: "v1"},
	}

	// Mock repository
	repo := &fixtures.RepoMock{
		FindEntitiesFunc: func(ctx context.Context, query dto.FindEntitiesQuery) (dto.Entities, error) {
			return expectedEntities, nil
		},
	}

	// Invoke the handler with query parameters
	authz := authorizertesting.New(testUserID)
	authz.WithTenant(&testTenantID)
	mw := []gin.HandlerFunc{authz.Middleware()}
	handlers := append(mw, datamodel.Find(repo)...)

	w, err := s.NewRequest("GET").
		WithPath("context_id", datamodelContextID).
		WithQuery("type", "TABLE").
		WithQuery("nameContains", "Table").
		WithQuery("first", "10").
		Send(handlers...)
	s.Require().NoError(err, "failed to send request")

	// Verify the response
	if !s.Equal(expectedStatus, w.Code) {
		s.FailNow(w.Body.String())
	}

	// Parse response
	var entities []models.Entity
	err = json.Unmarshal(w.Body.Bytes(), &entities)
	s.Require().NoError(err, "failed to unmarshal response")

	// Verify response content
	s.Equal(1, len(entities))
	s.Equal("ent_1", entities[0].ID)
	s.Equal("TableEntity", entities[0].Name)
	s.Equal("TABLE", entities[0].Type)

	// Verify the repository calls
	calls := repo.FindEntitiesCalls()
	s.Require().Equal(1, len(calls), "Must make exactly 1 repo.FindEntitiesCalls")
	s.Equal(testTenantID, calls[0].Query.TenantID)
	s.Equal(datamodelContextID, calls[0].Query.ContextID)
	s.Equal([]string{"TABLE"}, calls[0].Query.Types)
	s.Require().NotNil(calls[0].Query.NameContains)
	s.Equal("Table", *calls[0].Query.NameContains)
	s.Require().NotNil(calls[0].Query.First)
	s.Equal(10, *calls[0].Query.First)
}

func (s *ChainTestSuite) TestFindEntities_InvalidFirstParameter() {
	// Prepare test data
	testUserID := "pcp_123"
	testTenantID := "tnt_1"
	expectedStatus := http.StatusBadRequest
	datamodelContextID := "dst_111"

	// Mock repository
	repo := &fixtures.RepoMock{}

	// Invoke the handler with invalid first parameter
	authz := authorizertesting.New(testUserID)
	authz.WithTenant(&testTenantID)
	mw := []gin.HandlerFunc{authz.Middleware()}
	handlers := append(mw, datamodel.Find(repo)...)

	w, err := s.NewRequest("GET").
		WithPath("context_id", datamodelContextID).
		WithQuery("first", "invalid"). // Invalid first parameter
		Send(handlers...)
	s.Require().NoError(err, "failed to send request")

	// Verify the response
	s.Equal(expectedStatus, w.Code)

	// Verify no repository calls were made
	calls := repo.FindEntitiesCalls()
	s.Equal(0, len(calls), "Should not make any repo calls when validation fails")
}

func (s *ChainTestSuite) TestFindEntities_Unauthorized() {
	// Prepare test data
	expectedStatus := http.StatusUnauthorized
	datamodelContextID := "dst_111"

	// Mock repository
	repo := &fixtures.RepoMock{}

	// Invoke the handler with empty user ID (unauthorized)
	authz := authorizertesting.New("") // Empty user ID
	mw := []gin.HandlerFunc{authz.Middleware()}
	handlers := append(mw, datamodel.Find(repo)...)

	w, err := s.NewRequest("GET").
		WithPath("context_id", datamodelContextID).
		Send(handlers...)
	s.Require().NoError(err, "failed to send request")

	// Verify the response
	s.Equal(expectedStatus, w.Code)

	// Verify no repository calls were made
	calls := repo.FindEntitiesCalls()
	s.Equal(0, len(calls), "Should not make any repo calls when unauthorized")
}
