package datamodel

import (
	"context"
	"net/http"
	"strconv"
	"strings"

	"github.com/BackOfficeAssoc/catalog/models"
	"github.com/BackOfficeAssoc/catalog/repo"
	"github.com/BackOfficeAssoc/catalog/repo/dto"
	"github.com/BackOfficeAssoc/pkg/httperrors"
	"github.com/BackOfficeAssoc/qzar/pkg/authorizer"
	"github.com/gin-gonic/gin"
)

// NavigateResponse represents the response for the navigate API.
type NavigateResponse struct {
	Entities       []models.Entity        `json:"entities,omitempty"`
	EntityElements []models.EntityElement `json:"entity_elements,omitempty"`
	Relationships  []models.Relationship  `json:"relationships,omitempty"`
	NextToken      string                 `json:"next_token,omitempty"`
}

// NavigateRepo defines the repository interface for navigation
type NavigateRepo interface {
	NavigateDataModel(
		ctx context.Context,
		tenantID string,
		query models.NavigateDataModelQuery,
	) (dto.Entities, dto.EntityElements, dto.Relationships, string, error)
}

func NavigateDataModel(repository repo.DataModelStore) []gin.HandlerFunc {
	h := navigateHandler{
		repo: repository,
	}
	return h.Chain()
}

type navigateHandler struct {
	repo repo.DataModelStore
}

// navigateRequest holds all parameters for a data model navigation query.
type navigateRequest struct {
	ContextID       string
	ParentID        string
	Types           []string
	Filter          string
	First           int
	Offset          string
	Sort            string
	NameExact       string
	NameText        string
	DescriptionText string
}

// getQueryParam gets a query parameter using case-insensitive lookup
func getQueryParam(c *gin.Context, expectedParam string) string {
	// First let's try to get the exact expected parameter name
	if value := c.Query(expectedParam); value != "" {
		return value
	}
	// If not found, search without worrying about case-sensitivity
	expectedLower := strings.ToLower(expectedParam)
	for param, values := range c.Request.URL.Query() {
		if strings.ToLower(param) == expectedLower && len(values) > 0 {
			return values[0]
		}
	}

	return ""
}

func (h *navigateHandler) Validate(c *gin.Context) {
	datamodelContextID := c.Param("context_id")
	parentID := c.Query("parent_id")

	// Validation check - either context_id (from URL path) or parent_id required if query params exist

	if len(c.Request.URL.RawQuery) > 0 && datamodelContextID == "" && parentID == "" {
		httperrors.BadRequest(c, "Either context_id or parent_id is required")
		c.Abort()
		return
	}

	// Use QueryArray so I can catch multiple types in the request header
	types := c.QueryArray("type")
	if len(types) == 0 {
		types = []string{}
	}

	req := &navigateRequest{
		ContextID:       datamodelContextID,
		ParentID:        parentID,
		Types:           types,
		Filter:          c.Query("filter"),
		Offset:          c.Query("offset"),
		Sort:            c.Query("sort"),
		NameExact:       getQueryParam(c, "name_exact"),
		NameText:        getQueryParam(c, "name_text"),
		DescriptionText: getQueryParam(c, "description_text"),
	}

	// Pagination parameters
	firstStr := c.DefaultQuery("first", strconv.Itoa(repo.DefaultPageSize))
	first, err := strconv.Atoi(firstStr)
	if err != nil {
		first = repo.DefaultPageSize // Default value
	}
	// the clamping logic is at the repo layer but I just set a default value here
	req.First = first

	c.Set("request", req)
}

func (h *navigateHandler) Authorize(c *gin.Context) {
	// Get tenant ID from query parameters when provided, could be useful for global users
	tenantIDFromQuery := c.Query("tenant_id")

	authZ, err := authorizer.Output(c)
	if err != nil {
		httperrors.StatusText(c, http.StatusUnauthorized)
		c.Abort()
		return
	}

	var effectiveTenantID string

	if !authZ.HasGlobalAccess() { // User does NOT have global access
		if authZ.TenantID == nil {
			httperrors.StatusText(c, http.StatusUnauthorized)
			return
		}
		effectiveTenantID = *authZ.TenantID
	} else { // User HAS global access
		if tenantIDFromQuery != "" {
			effectiveTenantID = tenantIDFromQuery
		} else if authZ.TenantID != nil { // Global user whose token might still specify a default tenant
			effectiveTenantID = *authZ.TenantID
		} else {
			httperrors.BadRequest(c, "Global users must specify 'tenant_id' query parameter for this endpoint.")
			return
		}
	}

	// Set tenant ID and user ID in context
	c.Set("tenantID", effectiveTenantID)
	c.Set("user_id", authZ.UserID)
}

func (h *navigateHandler) Handle(c *gin.Context) {
	req := c.MustGet("request").(*navigateRequest)
	tenantID := c.MustGet("tenantID").(string)

	// Validate that context_id is required for any search
	if req.ContextID == "" {
		httperrors.BadRequest(c, "context_id is required for navigation")
		return
	}

	// Call the repository
	dtoEntities, dtoEntityElements, dtoRelationships, nextTokenString, repoErr := h.repo.NavigateDataModel(
		c.Request.Context(),
		tenantID,
		models.NavigateDataModelQuery{
			ContextID:       req.ContextID,
			ParentID:        req.ParentID,
			Types:           req.Types,
			Filter:          req.Filter,
			First:           req.First,
			Offset:          req.Offset,
			Sort:            req.Sort,
			NameExact:       req.NameExact,
			NameText:        req.NameText,
			DescriptionText: req.DescriptionText,
		},
	)

	if repoErr != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"error": repoErr.Error(),
		})
		return
	}

	resp := NavigateResponse{
		NextToken: nextTokenString,
	}

	if dtoEntities != nil && len(dtoEntities) > 0 {
		resp.Entities = make([]models.Entity, len(dtoEntities))
		for i, e := range dtoEntities {
			resp.Entities[i] = e.ToModel()
		}
	}

	if dtoEntityElements != nil && len(dtoEntityElements) > 0 {
		resp.EntityElements = make([]models.EntityElement, len(dtoEntityElements))
		for i, ee := range dtoEntityElements {
			resp.EntityElements[i] = *ee.ToModel()
		}
	}

	if dtoRelationships != nil && len(dtoRelationships) > 0 {
		resp.Relationships = make([]models.Relationship, len(dtoRelationships))
		for i, rel := range dtoRelationships {
			resp.Relationships[i] = rel.ToModel()
		}
	}

	c.JSON(http.StatusOK, resp)
}

func (h *navigateHandler) Chain() []gin.HandlerFunc {
	return []gin.HandlerFunc{
		h.Validate,
		h.Authorize,
		h.Handle,
	}
}
