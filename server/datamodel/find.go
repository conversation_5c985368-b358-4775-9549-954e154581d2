package datamodel

import (
	"fmt"
	"net/http"

	"github.com/gin-gonic/gin"

	"github.com/BackOfficeAssoc/catalog/models"
	"github.com/BackOfficeAssoc/catalog/repo"
	"github.com/BackOfficeAssoc/catalog/repo/dto"
	"github.com/BackOfficeAssoc/catalog/server/probes"
	"github.com/BackOfficeAssoc/pkg/httperrors"
	"github.com/BackOfficeAssoc/pkg/probe"
	"github.com/BackOfficeAssoc/qzar/pkg/authorizer"
)

func Find(repository repo.DataModelStore) []gin.HandlerFunc {
	h := findHandler{
		repo: repository,
	}
	return h.Chain()
}

type findHandler struct {
	repo repo.DataModelStore
}

type findRequest struct {
	ContextID           string
	Types               []string
	Filter              string
	First               int
	Offset              string
	Sort                string
	NameExact           string
	NameContains        string
	DescriptionContains string
}

func (h *findHandler) Validate(c *gin.Context) {
	contextID := c.Param("context_id")
	if contextID == "" {
		httperrors.BadRequest(c, "context_id is required")
		c.Abort()
		return
	}

	// Parse query parameters
	types := c.QueryArray("type")
	filter := c.Query("filter")
	sort := c.Query("sort")
	nameExact := c.Query("nameExact")
	nameContains := c.Query("nameContains")
	descriptionContains := c.Query("descriptionContains")
	offset := c.Query("offset")

	// Parse first parameter with default
	first := 100 // Default page size
	if firstStr := c.Query("first"); firstStr != "" {
		if parsedFirst, err := parseFirst(firstStr); err != nil {
			httperrors.BadRequest(c, "invalid 'first' parameter: %s", err.Error())
			c.Abort()
			return
		} else {
			first = parsedFirst
		}
	}

	req := &findRequest{
		ContextID:           contextID,
		Types:               types,
		Filter:              filter,
		First:               first,
		Offset:              offset,
		Sort:                sort,
		NameExact:           nameExact,
		NameContains:        nameContains,
		DescriptionContains: descriptionContains,
	}

	c.Set("request", req)
}

func (h *findHandler) Authorize(c *gin.Context) {
	// Get tenant ID from query parameters when provided, could be useful for global users
	tenantIDFromQuery := c.Query("tenant_id")

	authZ, err := authorizer.Output(c)
	if err != nil {
		probe.Load(c).Emit(probes.ProbeUnauthorized)
		httperrors.StatusText(c, http.StatusUnauthorized)
		c.Abort()
		return
	}

	var effectiveTenantID string

	if !authZ.HasGlobalAccess() { // User does NOT have global access
		if authZ.TenantID == nil {
			probe.Load(c).Emit(probes.ProbeUnauthorized)
			httperrors.StatusText(c, http.StatusUnauthorized)
			return
		}
		effectiveTenantID = *authZ.TenantID
	} else { // User HAS global access
		if tenantIDFromQuery != "" {
			effectiveTenantID = tenantIDFromQuery
		} else if authZ.TenantID != nil { // Global user whose token might still specify a default tenant
			effectiveTenantID = *authZ.TenantID
		} else {
			httperrors.BadRequest(c, "Global users must specify 'tenant_id' query parameter for this endpoint.")
			return
		}
	}

	// Set tenant ID and user ID in context
	c.Set("tenantID", effectiveTenantID)
	c.Set("user_id", authZ.UserID)
}

func (h *findHandler) Handle(c *gin.Context) {
	req := c.MustGet("request").(*findRequest)
	tenantID := c.MustGet("tenantID").(string)

	// Build the query for the repository using the same pattern as navigation
	query := dto.FindEntitiesQuery{
		TenantID:  tenantID,
		ContextID: req.ContextID,
		Types:     req.Types,
	}

	// Set optional parameters
	if req.Filter != "" {
		query.Filter = &req.Filter
	}
	if req.First > 0 {
		query.First = &req.First
	}
	if req.Offset != "" {
		query.Offset = &req.Offset
	}
	if req.Sort != "" {
		query.Sort = &req.Sort
	}
	if req.NameExact != "" {
		query.NameExact = &req.NameExact
	}
	if req.NameContains != "" {
		query.NameContains = &req.NameContains
	}
	if req.DescriptionContains != "" {
		query.DescriptionContains = &req.DescriptionContains
	}

	// Call the repository directly using FindEntities
	dtoEntities, err := h.repo.FindEntities(c.Request.Context(), query)
	if err != nil {
		probe.Load(c).WithError(err).Emit(probes.RepoError)
		httperrors.InternalServerError(c, "unexpected error while finding entities")
		return
	}

	// Convert DTOs to models
	entities := make([]models.Entity, len(dtoEntities))
	for i, dtoEntity := range dtoEntities {
		entities[i] = dtoEntity.ToModel()
	}

	c.JSON(http.StatusOK, entities)
}

func (h *findHandler) Chain() []gin.HandlerFunc {
	return []gin.HandlerFunc{
		h.Validate,
		h.Authorize,
		h.Handle,
	}
}

// parseFirst parses and validates the 'first' query parameter
func parseFirst(firstStr string) (int, error) {
	first := 0
	if _, err := fmt.Sscanf(firstStr, "%d", &first); err != nil {
		return 0, fmt.Errorf("must be a valid integer")
	}
	if first < 1 || first > 1000 {
		return 0, fmt.Errorf("must be between 1 and 1000")
	}
	return first, nil
}
