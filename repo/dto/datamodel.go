package dto

import (
	"fmt"
	"strings"

	"github.com/BackOfficeAssoc/catalog/models"
)

// Reference represents a reference to an entity or entity element
type Reference struct {
	Ref string `db:"ref"` // Changed from $ref to ref
}

// CreateDataModelInput represents the input for creating a data model
type CreateDataModelInput struct {
	MetaSchemaVersion string                 `db:"meta_schema_version"`
	ContextID         string                 `db:"context_id"`
	Namespace         string                 `db:"namespace"`
	CurrentVersion    string                 `db:"current_version"`
	Entities          []Entity               `db:"entities"`
	Relationships     []ExtendedRelationship `db:"relationships"`
}

// FromModel converts a model.Datamodel to dto.CreateDataModelInput
func (dto *CreateDataModelInput) FromModel(m models.DataModel) {
	dto.MetaSchemaVersion = m.MetaSchemaVersion
	dto.Namespace = m.Namespace
	dto.CurrentVersion = m.CurrentVersion

	// Convert entities
	dto.Entities = make([]Entity, len(m.Entities))
	for i, entity := range m.Entities {
		var dtoEntity Entity
		dtoEntity.FromModel(entity)
		dto.Entities[i] = dtoEntity
	}

	// Convert relationships
	dto.Relationships = make([]ExtendedRelationship, len(m.Relationships))
	for i, rel := range m.Relationships {
		var dtoRel ExtendedRelationship
		dtoRel.FromModel(rel)
		dto.Relationships[i] = dtoRel
	}
}

// DataModelResponse represents the response for data model operations
type DataModel struct {
	MetaSchemaVersion string `db:"meta_schema_version"`
	ContextID         string `db:"context_id"`
	Namespace         string `db:"namespace"`
	CurrentVersion    string `db:"current_version"`
	VersionID         string `db:"version_id"`
	CreatedBy         string `db:"created_by"`
	CreatedAt         string `db:"created_at"`
}

// ToModel converts a dto.DataModelResponse to models.DataModelResponse
func (dto DataModel) ToModel() models.DataModel {
	return models.DataModel{
		MetaSchemaVersion: dto.MetaSchemaVersion,
		Namespace:         dto.Namespace,
		CurrentVersion:    dto.CurrentVersion,
		// VersionID:               dto.VersionID,
		// CreatedBy:               dto.CreatedBy,
		// CreatedAt:               dto.CreatedAt,
	}
}

// FindEntitiesQuery represents the parameters for finding entities. and retrieves all fields from the database
type FindEntitiesQuery struct {
	TenantID        string   `db:"tenant_id"`
	ContextID       string   `db:"context_id"`
	Types           []string `db:"entity_type"`
	Filter          *string
	First           *int
	Offset          *string
	Sort            *string
	NameExact       *string `db:"name_exact"`
	NameText        *string `db:"name_contains"`
	DescriptionText *string `db:"description_contains"`
	IsRootLevel     bool    `db:"-"`
}

// Validate checks if the query is valid
func (q *FindEntitiesQuery) Validate() error {
	if q.TenantID == "" {
		return fmt.Errorf("TenantID is required for finding entities")
	}
	// ContextID can be empty for root level entities, so no validation here for it.

	// Convert Types to uppercase for case-insensitive matching
	if len(q.Types) > 0 {
		for i, t := range q.Types {
			q.Types[i] = strings.ToUpper(strings.TrimSpace(t))
		}
	}
	return nil
}

// Where builds the WHERE clause for the SQL query
func (q FindEntitiesQuery) Where() string {
	var conditions []string
	if !q.IsRootLevel && q.ContextID != "" {
		conditions = append(conditions, "context_id = :context_id")
	}
	if len(q.Types) > 0 {
		// Use UPPER on the column for case-insensitive matching
		conditions = append(conditions, "UPPER(entity_type) IN (:entity_type)")
	}
	if q.Filter != nil && *q.Filter != "" {
		conditions = append(conditions, "(name ILIKE '%' || :filter || '%' OR description ILIKE '%' || :filter || '%')")
	}
	if q.NameExact != nil && *q.NameExact != "" {
		conditions = append(conditions, "name = :name_exact")
	}
	if q.NameText != nil && *q.NameText != "" {
		conditions = append(conditions, "name ILIKE '%' || :name_contains || '%'")
	}
	if q.DescriptionText != nil && *q.DescriptionText != "" {
		conditions = append(conditions, "to_tsvector('english', description) @@ plainto_tsquery('english', :description_contains)")
	}
	if q.Offset != nil && *q.Offset != "" {
		conditions = append(conditions, "id > :offset")
	}
	if len(conditions) == 0 {
		return "WHERE 1=1"
	}
	return "WHERE " + strings.Join(conditions, " AND ")
}

// OrderBy builds the ORDER BY clause for the SQL query
func (q FindEntitiesQuery) OrderBy() string {
	if q.Sort != nil && *q.Sort != "" {
		safeSort := SanitizeSortString(*q.Sort)
		if safeSort != "" {
			return "ORDER BY " + safeSort
		}
	}
	return "ORDER BY name ASC, id ASC" // Default sort
}

// Limit builds the LIMIT clause for the SQL query
func (q FindEntitiesQuery) Limit() string {
	if q.First != nil && *q.First > 0 {
		return fmt.Sprintf("LIMIT %d", *q.First+1)
	}
	return ""
}

// FindEntityElementsQuery represents the parameters for finding entity elements. and also retrieves the elements from the database
type FindEntityElementsQuery struct {
	TenantID        string   `db:"tenant_id"`
	ParentID        string   `db:"parent_id"`
	ContextID       *string  `db:"context_id"`
	Types           []string `db:"element_type"`
	Filter          *string
	First           *int
	Offset          *string
	Sort            *string
	NameExact       *string `db:"name_exact"`
	NameText        *string `db:"name_contains"`
	DescriptionText *string `db:"description_contains"`
}

// Validate checks if the query is valid
func (q *FindEntityElementsQuery) Validate() error {
	if q.TenantID == "" {
		return fmt.Errorf("TenantID is required for finding entity elements")
	}
	if q.ParentID == "" {
		return fmt.Errorf("ParentID is required for finding entity elements")
	}

	// Convert Types to uppercase for case-insensitive matching
	if len(q.Types) > 0 {
		for i, t := range q.Types {
			q.Types[i] = strings.ToUpper(strings.TrimSpace(t))
		}
	}
	return nil
}

// Where builds the WHERE clause for the SQL query
func (q FindEntityElementsQuery) Where() string {
	var conditions []string
	conditions = append(conditions, "parent_id = :parent_id")

	if q.ContextID != nil && *q.ContextID != "" {
		conditions = append(conditions, "context_id = :context_id")
	}
	if len(q.Types) > 0 {
		conditions = append(conditions, "UPPER(element_type) IN (:element_type)")
	}
	if q.Filter != nil && *q.Filter != "" {
		conditions = append(conditions, "(name ILIKE '%' || :filter || '%' OR description ILIKE '%' || :filter || '%')")
	}
	if q.NameExact != nil && *q.NameExact != "" {
		conditions = append(conditions, "name = :name_exact")
	}
	if q.NameText != nil && *q.NameText != "" {
		conditions = append(conditions, "name ILIKE '%' || :name_contains || '%'")
	}
	if q.DescriptionText != nil && *q.DescriptionText != "" {
		conditions = append(conditions, "to_tsvector('english', description) @@ plainto_tsquery('english', :description_contains)")
	}
	if q.Offset != nil && *q.Offset != "" {
		conditions = append(conditions, "id > :offset")
	}

	if len(conditions) == 0 {
		return "WHERE 1=1" // Fallback,
	}
	return "WHERE " + strings.Join(conditions, " AND ")
}

// OrderBy builds the ORDER BY clause for the SQL query
func (q FindEntityElementsQuery) OrderBy() string {
	if q.Sort != nil && *q.Sort != "" {
		safeSort := SanitizeSortString(*q.Sort)
		if safeSort != "" {
			return "ORDER BY " + safeSort
		}
	}
	return "ORDER BY name ASC, id ASC"
}

func (q FindEntityElementsQuery) Limit() string {
	if q.First != nil && *q.First > 0 {
		return fmt.Sprintf("LIMIT %d", *q.First+1) // +1 to check for next page
	}
	return "" // No limit
}
