package models

import "time"

type DataModel struct {
	MetaSchemaVersion string         `json:"meta_schema_version"`
	Namespace         string         `json:"namespace"`
	CurrentVersion    string         `json:"current_version"`
	Entities          []Entity       `json:"entities"`
	Relationships     []Relationship `json:"relationships"`
	TenantID          string         `json:"tenant_id" binding:"required"`
}

type Entity struct {
	ModelName        string                 `json:"model_name"`
	Name             string                 `json:"name"`
	Type             string                 `json:"type"`
	ConsumptionType  string                 `json:"consumption_type"`
	Description      string                 `json:"description,omitempty"`
	Active           bool                   `json:"active"`
	Properties       map[string]interface{} `json:"properties,omitempty"`
	CustomProperties map[string]interface{} `json:"custom_properties,omitempty"`
	Tags             []string               `json:"tags"`
	EntityElements   []EntityElement        `json:"entity_elements"`
	TenantID         string                 `json:"tenant_id"`
	ID               string                 `json:"id"`
	MetaVersion      string                 `json:"meta_version"`
	ContextID        string                 `json:"context_id"`
	OrdinalPosition  int                    `json:"ordinal_position,omitempty"`
	BaseType         string                 `json:"base_type"`
	Ref              string                 `json:"ref"`
	MappedEntity     *string                `json:"mapped_entity"` // Changed to pointer to handle NULL values
	CreatedAt        string                 `json:"created_at"`
	CreatedBy        string                 `json:"created_by"`
	VersionID        string                 `json:"version_id"`
	BranchID         string                 `json:"branch_id"`
}

type EntityElement struct {
	ModelName        string                 `json:"model_name"`
	Name             string                 `json:"name"`
	Type             string                 `json:"type"`
	ConsumptionType  string                 `json:"consumption_type"`
	Description      string                 `json:"description,omitempty"`
	Active           bool                   `json:"active,omitempty"`
	OrdinalPosition  int                    `json:"ordinal_position,omitempty"`
	DataType         string                 `json:"data_type"`
	DefaultValue     string                 `json:"default_value,omitempty"`
	Precision        int                    `json:"precision,omitempty"`
	Scale            int                    `json:"scale,omitempty"`
	MaxLength        int                    `json:"max_length,omitempty"`
	IsRequired       bool                   `json:"is_required,omitempty"`
	IsArray          bool                   `json:"is_array,omitempty"`
	Tags             []string               `json:"tags,omitempty"`
	Properties       map[string]interface{} `json:"properties"`
	CustomProperties map[string]interface{} `json:"custom_properties,omitempty"`
	// Database-specific fields
	TenantID      string `json:"tenant_id,omitempty"`
	ID            string `json:"id,omitempty"`
	MetaVersion   string `json:"meta_version,omitempty"`
	ContextID     string `json:"context_id,omitempty"`
	ParentID      string `json:"parent_id,omitempty"`
	Ref           string `json:"ref,omitempty"`
	MappedElement string `json:"mapped_element,omitempty"`
	CreatedAt     string `json:"created_at,omitempty"`
	CreatedBy     string `json:"created_by,omitempty"`
	VersionID     string `json:"version_id,omitempty"`
	BranchID      string `json:"branch_id,omitempty"`
}

type Relationship struct {
	ModelName        string                 `json:"model_name"`
	Name             string                 `json:"name"`
	Type             string                 `json:"type"`
	Description      string                 `json:"description,omitempty"`
	OrdinalPosition  int                    `json:"ordinal_position,omitempty"`
	Active           bool                   `json:"active,omitempty"`
	Source           Reference              `json:"source"`
	Target           Reference              `json:"target"`
	Properties       map[string]interface{} `json:"properties,omitempty"`
	CustomProperties map[string]interface{} `json:"custom_properties,omitempty"`
	// Database-specific fields
	TenantID    string `json:"tenant_id,omitempty"`
	ID          string `json:"id,omitempty"`
	MetaVersion string `json:"meta_version,omitempty"`
	ContextID   string `json:"context_id,omitempty"`
	SourceID    string `json:"source_id,omitempty"`
	TargetID    string `json:"target_id,omitempty"`
	CreatedAt   string `json:"created_at,omitempty"`
	CreatedBy   string `json:"created_by,omitempty"`
	VersionID   string `json:"version_id,omitempty"`
	BranchID    string `json:"branch_id,omitempty"`
}

type Reference struct {
	Ref string `json:"$ref"`
}

type FindEntityQuery struct {
	TenantID  string  `form:"tenant_id" url:"tenant_id,omitempty"`
	ID        *string `form:"id" url:"id,omitempty"`
	Name      *string `form:"name" url:"name,omitempty"`
	CreatedBy *string `form:"created_by" url:"created_by,omitempty"`

	After *string `form:"after" url:"after,omitempty"`
	First *int    `form:"first" url:"first,omitempty"`
}

type DeleteEntityParams struct {
	TenantID string `json:"tenant_id"`
	ID       string `json:"id"`
}

type Branch struct {
	ID             string     `json:"id"`
	TenantID       string     `json:"tenant_id"`
	Name           string     `json:"name"`
	FromBranchID   *string    `json:"from_branch_id,omitempty"`
	FromVersionID  *string    `json:"from_version_id,omitempty"`
	FromVersionTag *string    `json:"from_version_tag,omitempty"`
	CreatedAt      time.Time  `json:"created_at"`
	CreatedBy      string     `json:"created_by"`
	ModifiedAt     *time.Time `json:"modified_at,omitempty"`
	ModifiedBy     *string    `json:"modified_by,omitempty"`
	DeletedAt      *time.Time `json:"deleted_at,omitempty"`
	DeletedBy      *string    `json:"deleted_by,omitempty"`
}

type FindBranchQuery struct {
	TenantID  *string `form:"tenant_id" url:"tenant_id,omitempty"`
	ID        *string `form:"id" url:"id,omitempty"`
	Name      *string `form:"name" url:"name,omitempty"`
	CreatedBy *string `form:"created_by" url:"created_by,omitempty"`

	After *string `form:"after" url:"after,omitempty"`
	First *int    `form:"first" url:"first,omitempty"`
}

type DeleteBranchParams struct {
	TenantID string `json:"tenant_id"`
	ID       string `json:"id"`
}

type Version struct {
	ID          string     `json:"id"`
	TenantID    string     `json:"tenant_id"`
	ContextID   string     `json:"context_id"`
	BranchID    string     `json:"branch_id"`
	VersionTag  string     `json:"version_tag"`
	StartedAt   time.Time  `json:"started_at"`
	CompletedAt time.Time  `json:"completed_at"`
	ImportID    string     `json:"import_id"`
	CreatedAt   time.Time  `json:"created_at"`
	CreatedBy   string     `json:"created_by"`
	DeletedAt   *time.Time `json:"deleted_at,omitempty"`
	DeletedBy   *string    `json:"deleted_by,omitempty"`
}

type FindVersionQuery struct {
	TenantID   *string `form:"tenant_id" url:"tenant_id,omitempty"`
	ID         *string `form:"id" url:"id,omitempty"`
	BranchID   *string `form:"branch_id" url:"branch_id,omitempty"`
	VersionTag *string `form:"version_tag" url:"version_tag,omitempty"`
	ContextID  *string `form:"context_id" url:"context_id,omitempty"`
	CreatedBy  *string `form:"created_by" url:"created_by,omitempty"`

	After *string `form:"after" url:"after,omitempty"`
	First *int    `form:"first" url:"first,omitempty"`
}

type DeleteVersionParams struct {
	TenantID   string `json:"tenant_id"`
	ID         string `json:"id"`
	BranchID   string `json:"branch_id"`
	VersionTag string `json:"version_tag"`
	ContextID  string `json:"context_id"`
}

type CreateDataModelRequest struct {
	MetaSchemaVersion string         `json:"meta_schema_version" binding:"required"`
	Namespace         string         `json:"namespace"`
	Entities          []Entity       `json:"entities" binding:"required"`
	Relationships     []Relationship `json:"relationships"`

	TenantID *string `json:"tenant_id"`
}

type CreateDataModelResponse struct {
	MetaSchemaVersion string `json:"meta_schema_version" binding:"required"`
	ContextID         string `json:"context_id" binding:"required"`
	NewVersion        string `json:"new_version" binding:"required"`
}

// NavigateDataModelQuery represents the query parameters for navigating a data model.
type NavigateDataModelQuery struct {
	ContextID       string   `json:"context_id,omitempty" form:"context_id"`
	ParentID        string   `json:"parent_id,omitempty" form:"parent_id"`
	Types           []string `json:"types,omitempty" form:"type"` // form uses "type" for multiple values
	Filter          string   `json:"filter,omitempty" form:"filter"`
	First           int      `json:"first,omitempty" form:"first"`
	Offset          string   `json:"offset,omitempty" form:"offset"`
	Sort            string   `json:"sort,omitempty" form:"sort"`
	NameExact       string   `json:"name_exact,omitempty" form:"nameExact"`
	NameText        string   `json:"name_text,omitempty" form:"nameText"`
	DescriptionText string   `json:"description_text,omitempty" form:"descriptionText"`
}
